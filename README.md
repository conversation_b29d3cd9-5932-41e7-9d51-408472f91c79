# 项目结构说明

项目已重新组织为前后端分离的结构：

## 📁 目录结构

```
test3/
├── api/                    # 后端文件
│   ├── app.py             # Flask 应用主文件
│   ├── models.py          # 数据库模型
│   ├── requirements.txt   # Python 依赖
│   └── instance/          # 数据库文件
│       └── users.db
├── front/                 # 前端文件
│   ├── templates/         # HTML 模板
│   │   ├── login.html
│   │   └── profile.html
│   └── static/           # 静态资源
│       ├── css/
│       │   └── style.css
│       ├── js/
│       └── images/
│           └── road.jpg
├── images/               # 其他图片资源
└── 运行截图/             # 项目截图
```

## 🚀 运行方式

1. 进入 api 目录：
   ```bash
   cd api
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 运行应用：
   ```bash
   python app.py
   ```

## 📝 说明

- **前端文件** (`front/`): 包含所有 HTML 模板和静态资源（CSS、JS、图片）
- **后端文件** (`api/`): 包含 Flask 应用代码、数据库模型和配置
- Flask 应用已配置为从 `../front/` 目录读取模板和静态文件
- 数据库文件位于 `api/instance/` 目录中

## 🔧 修改内容

- 将模板文件从根目录的 `templates/` 移动到 `front/templates/`
- 将静态文件从根目录的 `static/` 移动到 `front/static/`
- 将后端代码移动到 `api/` 目录
- 更新了 `app.py` 中的模板和静态文件路径配置
