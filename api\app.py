from flask import Flask, request, jsonify, render_template
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, jwt_required, create_access_token, get_jwt_identity
from models import db, User
from datetime import timedelta

app = Flask(__name__,
            template_folder='../front/templates',
            static_folder='../front/static')

# 配置
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///users.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = 'jwt-secret-string-change-in-production'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# 初始化扩展
db.init_app(app)
jwt = JWTManager(app)

# 路由
@app.route('/')
def index():
    return render_template('login.html')

@app.route('/profile')
def profile():
    return render_template('profile.html')

@app.route('/api/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            access_token = create_access_token(identity=str(user.id))
            return jsonify({
                'success': True,
                'token': access_token,
                'user': user.to_dict()
            })
        else:
            return jsonify({'error': '用户名或密码错误'}), 401

    except Exception as e:
        return jsonify({'error': '登录失败'}), 500

@app.route('/api/update-bio', methods=['POST'])
@jwt_required()
def update_bio():
    try:
        user_id = int(get_jwt_identity())
        data = request.get_json()
        new_bio = data.get('bio', '')

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': '用户不存在'}), 404

        user.bio = new_bio
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '个人简介更新成功',
            'user': user.to_dict()
        })

    except Exception as e:
        return jsonify({'error': '更新失败'}), 500

@app.route('/api/user', methods=['GET'])
@jwt_required()
def get_user():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': '用户不存在'}), 404

        return jsonify({
            'success': True,
            'user': user.to_dict()
        })

    except Exception as e:
        return jsonify({'error': '获取用户信息失败'}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # 创建测试用户（如果不存在）
        if not User.query.filter_by(username='admin').first():
            test_user = User(username='admin', bio='这是我的个人简介')
            test_user.set_password('123456')
            db.session.add(test_user)
            db.session.commit()
            print("创建测试用户: admin / 123456")

    app.run(debug=True)
