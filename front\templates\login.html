<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-sm mt-5">
                    <div class="card-body">
                        <h2 class="card-title text-center mb-4">用户登录</h2>

                        <div id="alert-container"></div>

                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">登录密码</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="loginBtn">
                                    <span id="loginBtnText">登录</span>
                                    <span id="loginSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-3">
                            <small class="text-muted">测试账号: admin / 123456</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginSpinner = document.getElementById('loginSpinner');
            const alertContainer = document.getElementById('alert-container');

            // 显示加载状态
            loginBtn.disabled = true;
            loginBtnText.textContent = '登录中...';
            loginSpinner.classList.remove('d-none');

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (data.success) {
                    // 保存token和用户信息
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));

                    // 显示成功消息
                    alertContainer.innerHTML = `
                        <div class="alert alert-success" role="alert">
                            登录成功！正在跳转...
                        </div>
                    `;

                    // 跳转到个人主页
                    setTimeout(() => {
                        window.location.href = '/profile';
                    }, 1000);
                } else {
                    // 显示错误消息
                    alertContainer.innerHTML = `
                        <div class="alert alert-danger" role="alert">
                            ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                alertContainer.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        网络错误，请稍后重试
                    </div>
                `;
            } finally {
                // 恢复按钮状态
                loginBtn.disabled = false;
                loginBtnText.textContent = '登录';
                loginSpinner.classList.add('d-none');
            }
        });
    </script>
</body>
</html>
